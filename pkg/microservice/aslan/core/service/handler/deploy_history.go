package handler

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	_ "github.com/koderover/zadig/v2/pkg/microservice/aslan/core/common/repository/models"
	commonservice "github.com/koderover/zadig/v2/pkg/microservice/aslan/core/common/service"
	"github.com/koderover/zadig/v2/pkg/microservice/aslan/core/service/service"
	internalhandler "github.com/koderover/zadig/v2/pkg/shared/handler"
	e "github.com/koderover/zadig/v2/pkg/tool/errors"
)

type ServiceDeployHistoryResp struct {
	Total int64                                          `json:"total"`
	Rows  []commonservice.ListEnvServiceVersionsResponse `json:"rows"`
}

// @Summary List Service Deploy History
// @Description List Service Deploy History
// @Tags 	service
// @Accept 	json
// @Produce json
// @Param 	project_name		query		string							false	"project name"
// @Param 	env_name		    query		string							false	"env name"
// @Param 	service_name		query		string							false	"service name"
// @Param 	create_by			query		string							false	"create account name"
// @Param   page 				query       int                             false   "page"
// @Param   page_size 			query       int                             false   "page size"
// @Success 200 			{array}  	ServiceDeployHistoryResp
// @Router /api/aslan/service/deploy_history [get]
func ListServiceDeployHistory(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {
		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	projectKey := c.Query("project_name")
	envName := c.Query("env_name")
	serviceName := c.Query("service_name")
	page, err := strconv.Atoi(c.Query("page"))
	if err != nil {
		page = 1
	}
	pageSize, err := strconv.Atoi(c.Query("page_size"))
	if err != nil {
		page = 50
	}
	createdBy := c.Query("create_by")

	// authorization checks
	var (
		data  []commonservice.ListEnvServiceVersionsResponse
		count int64
	)
	if !ctx.Resources.IsSystemAdmin {
		if projectKey != "" {
			if _, ok := ctx.Resources.ProjectAuthInfo[projectKey]; !ok {
				ctx.UnAuthorized = true
				return
			}

			if projectKey != "" {
				if !ctx.Resources.ProjectAuthInfo[projectKey].IsProjectAdmin &&
					!ctx.Resources.ProjectAuthInfo[projectKey].ProductionService.View &&
					!ctx.Resources.ProjectAuthInfo[projectKey].Service.View {
					ctx.UnAuthorized = true
					return
				}
			}

			// list all selected services history
			data, count, err = commonservice.ListEnvServiceVersions(ctx, projectKey, envName, serviceName, createdBy, page, pageSize, ctx.Logger)

		} else {
			// list created By me
			// TOOD: check service project
			data, count, err = commonservice.ListEnvServiceVersions(ctx, projectKey, envName, serviceName, ctx.Account, page, pageSize, ctx.Logger)

		}

	} else {
		data, count, err = commonservice.ListEnvServiceVersions(ctx, projectKey, envName, serviceName, createdBy, page, pageSize, ctx.Logger)
	}

	if err != nil {
		ctx.RespErr = err
		return
	}

	ctx.Resp = ServiceDeployHistoryResp{
		Total: count,
		Rows:  data,
	}

}

// @Summary List Service Max Deploy Versions By env
// @Description List Service Deploy Versions By env
// @Tags 	service
// @Accept 	json
// @Produce json
// @Param 	serviceName		path		string							true	"service name"
// @Param 	projectName		query		string							true	"project name"
// @Success 200 			{array}  	models.EnvServiceVersion
// @Router /api/aslan/service/deploy_history/max_version/{serviceName} [get]
func ListServiceMaxVersionByEnv(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {
		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}
	projectKey := c.Query("projectName")
	if projectKey == "" {
		ctx.RespErr = e.ErrInvalidParam.AddDesc("empty projectName")
		return
	}
	serviceName := c.Param("serviceName")
	if serviceName == "" {
		ctx.RespErr = e.ErrInvalidParam.AddDesc("empty serviceName")
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		if _, ok := ctx.Resources.ProjectAuthInfo[projectKey]; !ok {
			ctx.UnAuthorized = true
			return
		}

		if !ctx.Resources.ProjectAuthInfo[projectKey].IsProjectAdmin &&
			!ctx.Resources.ProjectAuthInfo[projectKey].Service.View {
			ctx.UnAuthorized = true
			return
		}
	}

	ctx.Resp, ctx.RespErr = service.ListServiceMaxVersionByEnv(serviceName, projectKey)
}
