/*
 * Copyright 2022 The KodeRover Authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package handler

import (
	"fmt"

	"github.com/gin-gonic/gin"

	commonmodels "github.com/koderover/zadig/v2/pkg/microservice/aslan/core/common/repository/models"
	commonutil "github.com/koderover/zadig/v2/pkg/microservice/aslan/core/common/util"
	"github.com/koderover/zadig/v2/pkg/microservice/aslan/core/system/service"
	internalhandler "github.com/koderover/zadig/v2/pkg/shared/handler"
	e "github.com/koderover/zadig/v2/pkg/tool/errors"
)

// @Summary      获取通知应用列表
// @Description  获取通知应用列表
// @Tags         system
// @Accept       json
// @Produce      json
// @Success      200          {array}  models.IMApp  "角色绑定列表"
// @Router       /api/aslan/system/im_app [get]
func ListIMApp(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	// TODO: Authorization leak
	// comment: this API should only be used when the user requires IM app's full information, including AK/SK
	// however this is currently used in multiple situation, thus having serious security leaks.

	//if !ctx.Resources.IsSystemAdmin {
	//	ctx.UnAuthorized = true
	//	return
	//}

	ctx.Resp, ctx.RespErr = service.ListIMApp(c.Query("type"), ctx.Logger)
}

// @Summary      添加通知应用
// @Description  添加通知应用
// @Tags         system
// @Accept       json
// @Produce      json
// @Param 		 body 		body 		models.IMApp  true 	"body"
// @Success      200
// @Router       /api/aslan/system/im_app [post]
func CreateIMApp(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {
		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	var args commonmodels.IMApp
	if err := c.ShouldBindJSON(&args); err != nil {
		ctx.RespErr = e.ErrInvalidParam.AddErr(err)
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	err = commonutil.CheckZadigProfessionalLicense()
	if err != nil {
		ctx.RespErr = err
		return
	}

	ctx.RespErr = service.CreateIMApp(&args, ctx.Logger)
}

// @Summary      更新通知应用
// @Description  更新通知应用
// @Tags         system
// @Accept       json
// @Produce      json
// @Param 		 id 		path 		string  true 	"app id"
// @Param 		 body 		body 		models.IMApp  true 	"body"
// @Success      200
// @Router       /api/aslan/system/im_app/{id} [put]
func UpdateIMApp(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	var args commonmodels.IMApp
	if err := c.ShouldBindJSON(&args); err != nil {
		ctx.RespErr = e.ErrInvalidParam.AddErr(err)
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	ctx.RespErr = service.UpdateIMApp(c.Param("id"), &args, ctx.Logger)
}

// @Summary      更新通知应用
// @Description  更新通知应用
// @Tags         system
// @Accept       json
// @Produce      json
// @Param 		 id 		path 		string  true 	"app id"
// @Success      200
// @Router       /api/aslan/system/im_app/{id} [delete]
func DeleteIMApp(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	ctx.RespErr = service.DeleteIMApp(c.Param("id"), ctx.Logger)
}

// @Summary      检验通知应用
// @Description  检验通知应用
// @Tags         system
// @Accept       json
// @Produce      json
// @Param 		 body 		body 		models.IMApp  true 	"body"
// @Success      200
// @Router       /api/aslan/system/im_app/validate [post]
func ValidateIMApp(c *gin.Context) {
	ctx, err := internalhandler.NewContextWithAuthorization(c)
	defer func() { internalhandler.JSONResponse(c, ctx) }()

	if err != nil {

		ctx.RespErr = fmt.Errorf("authorization Info Generation failed: err %s", err)
		ctx.UnAuthorized = true
		return
	}

	var args commonmodels.IMApp
	if err := c.ShouldBindJSON(&args); err != nil {
		ctx.RespErr = e.ErrInvalidParam.AddErr(err)
		return
	}

	// authorization checks
	if !ctx.Resources.IsSystemAdmin {
		ctx.UnAuthorized = true
		return
	}

	ctx.RespErr = service.ValidateIMApp(&args, ctx.Logger)
}
