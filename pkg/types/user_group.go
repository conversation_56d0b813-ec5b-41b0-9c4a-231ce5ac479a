/*
Copyright 2023 The KodeRover Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package types

import "github.com/koderover/zadig/v2/pkg/microservice/user/core/repository/models"

const (
	AllUserGroupName = "所有用户"
)

type DetailedUserGroupResp struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Type        string         `json:"type"`
	UIDs        []string       `json:"uids"`
	Users       []*models.User `json:"users"`
}

type UserGroupResp struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Type        string `json:"type"`
	UserTotal   int64  `json:"user_total"`
}

type ListUserGroupResp struct {
	GroupList []*UserGroupResp `json:"group_list"`
	Count     int64            `json:"total"`
}
